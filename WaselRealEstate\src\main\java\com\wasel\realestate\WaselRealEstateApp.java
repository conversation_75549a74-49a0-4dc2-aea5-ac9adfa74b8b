package com.wasel.realestate;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * تطبيق واصل عقار - الكلاس الرئيسي
 * تطبيق إدارة العقارات والمستأجرين
 */
public class WaselRealEstateApp extends JFrame {
    
    private static final String APP_TITLE = "واصل عقار - إدارة العقارات والمستأجرين";
    private static final String VERSION = "1.0.0";
    
    private JPanel mainPanel;
    private JPanel sidebarPanel;
    private JPanel contentPanel;
    
    public WaselRealEstateApp() {
        initializeApp();
        setupUI();
        showMainScreen();
    }
    
    private void initializeApp() {
        setTitle(APP_TITLE);
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        setMinimumSize(new Dimension(1000, 600));
        
        // تعيين Look and Feel حديث
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void setupUI() {
        mainPanel = new JPanel(new BorderLayout());
        
        // إنشاء الشريط الجانبي
        createSidebar();
        
        // إنشاء منطقة المحتوى
        contentPanel = new JPanel(new BorderLayout());
        contentPanel.setBackground(Color.WHITE);
        contentPanel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        mainPanel.add(sidebarPanel, BorderLayout.WEST);
        mainPanel.add(contentPanel, BorderLayout.CENTER);
        
        add(mainPanel);
    }
    
    private void createSidebar() {
        sidebarPanel = new JPanel();
        sidebarPanel.setLayout(new BoxLayout(sidebarPanel, BoxLayout.Y_AXIS));
        sidebarPanel.setBackground(new Color(45, 55, 72));
        sidebarPanel.setPreferredSize(new Dimension(250, 0));
        sidebarPanel.setBorder(BorderFactory.createEmptyBorder(20, 15, 20, 15));
        
        // شعار التطبيق
        JLabel logoLabel = new JLabel("واصل عقار", SwingConstants.CENTER);
        logoLabel.setFont(new Font("Arial", Font.BOLD, 24));
        logoLabel.setForeground(Color.WHITE);
        logoLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        logoLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 30, 0));
        
        sidebarPanel.add(logoLabel);
        
        // أزرار القائمة
        addMenuButton("الشاشة الرئيسية", "🏠", true, e -> showMainScreen());
        addMenuButton("الإعدادات", "⚙️", false, e -> showSettingsScreen());
        
        // مساحة فارغة
        sidebarPanel.add(Box.createVerticalGlue());
        
        // معلومات الإصدار
        JLabel versionLabel = new JLabel("الإصدار " + VERSION, SwingConstants.CENTER);
        versionLabel.setFont(new Font("Arial", Font.PLAIN, 12));
        versionLabel.setForeground(new Color(160, 174, 192));
        versionLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        sidebarPanel.add(versionLabel);
    }
    
    private void addMenuButton(String text, String icon, boolean isActive, ActionListener action) {
        JButton button = new JButton(icon + "  " + text);
        button.setFont(new Font("Arial", Font.PLAIN, 14));
        button.setAlignmentX(Component.CENTER_ALIGNMENT);
        button.setMaximumSize(new Dimension(220, 45));
        button.setPreferredSize(new Dimension(220, 45));
        button.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));
        button.setFocusPainted(false);
        button.setHorizontalAlignment(SwingConstants.LEFT);
        
        if (isActive) {
            button.setBackground(new Color(66, 153, 225));
            button.setForeground(Color.WHITE);
        } else {
            button.setBackground(new Color(45, 55, 72));
            button.setForeground(new Color(203, 213, 224));
        }
        
        button.addActionListener(action);
        
        // تأثير hover
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                if (!isActive) {
                    button.setBackground(new Color(74, 85, 104));
                }
            }
            public void mouseExited(java.awt.event.MouseEvent evt) {
                if (!isActive) {
                    button.setBackground(new Color(45, 55, 72));
                }
            }
        });
        
        sidebarPanel.add(button);
        sidebarPanel.add(Box.createRigidArea(new Dimension(0, 10)));
    }
    
    private void showMainScreen() {
        contentPanel.removeAll();
        
        JPanel mainScreen = new JPanel(new BorderLayout());
        mainScreen.setBackground(Color.WHITE);
        
        // العنوان الرئيسي
        JLabel titleLabel = new JLabel("مرحباً بك في واصل عقار", SwingConstants.CENTER);
        titleLabel.setFont(new Font("Arial", Font.BOLD, 32));
        titleLabel.setForeground(new Color(45, 55, 72));
        titleLabel.setBorder(BorderFactory.createEmptyBorder(50, 0, 30, 0));
        
        // الوصف
        JLabel descLabel = new JLabel("<html><center>نظام شامل لإدارة العقارات والمستأجرين<br>مع إمكانيات متقدمة للتحكم والمتابعة</center></html>", SwingConstants.CENTER);
        descLabel.setFont(new Font("Arial", Font.PLAIN, 18));
        descLabel.setForeground(new Color(113, 128, 150));
        
        // لوحة الإحصائيات السريعة
        JPanel statsPanel = createStatsPanel();
        
        mainScreen.add(titleLabel, BorderLayout.NORTH);
        mainScreen.add(descLabel, BorderLayout.CENTER);
        mainScreen.add(statsPanel, BorderLayout.SOUTH);
        
        contentPanel.add(mainScreen);
        contentPanel.revalidate();
        contentPanel.repaint();
    }
    
    private JPanel createStatsPanel() {
        JPanel statsPanel = new JPanel(new GridLayout(1, 4, 20, 0));
        statsPanel.setBackground(Color.WHITE);
        statsPanel.setBorder(BorderFactory.createEmptyBorder(50, 0, 50, 0));
        
        statsPanel.add(createStatCard("العقارات", "0", "🏢"));
        statsPanel.add(createStatCard("المستأجرين", "0", "👥"));
        statsPanel.add(createStatCard("العقود", "0", "📋"));
        statsPanel.add(createStatCard("الإيرادات", "0 ريال", "💰"));
        
        return statsPanel;
    }
    
    private JPanel createStatCard(String title, String value, String icon) {
        JPanel card = new JPanel(new BorderLayout());
        card.setBackground(new Color(247, 250, 252));
        card.setBorder(BorderFactory.createCompoundBorder(
            BorderFactory.createLineBorder(new Color(226, 232, 240), 1),
            BorderFactory.createEmptyBorder(20, 15, 20, 15)
        ));
        
        JLabel iconLabel = new JLabel(icon, SwingConstants.CENTER);
        iconLabel.setFont(new Font("Arial", Font.PLAIN, 24));
        
        JLabel titleLabel = new JLabel(title, SwingConstants.CENTER);
        titleLabel.setFont(new Font("Arial", Font.PLAIN, 14));
        titleLabel.setForeground(new Color(113, 128, 150));
        
        JLabel valueLabel = new JLabel(value, SwingConstants.CENTER);
        valueLabel.setFont(new Font("Arial", Font.BOLD, 20));
        valueLabel.setForeground(new Color(45, 55, 72));
        
        JPanel textPanel = new JPanel(new GridLayout(2, 1));
        textPanel.setBackground(new Color(247, 250, 252));
        textPanel.add(titleLabel);
        textPanel.add(valueLabel);
        
        card.add(iconLabel, BorderLayout.NORTH);
        card.add(textPanel, BorderLayout.CENTER);
        
        return card;
    }
    
    private void showSettingsScreen() {
        contentPanel.removeAll();
        
        SettingsPanel settingsPanel = new SettingsPanel();
        contentPanel.add(settingsPanel);
        
        contentPanel.revalidate();
        contentPanel.repaint();
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new WaselRealEstateApp().setVisible(true);
        });
    }
}
